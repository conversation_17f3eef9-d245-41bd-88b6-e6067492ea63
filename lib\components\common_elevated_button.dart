import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gatepass_flutter/utils/colors.dart';

class CommonElevatedButton extends StatelessWidget {
  final String text;
  final Color? buttonColor;
  final Color? shadowColor;
  final Color? textColor;
  final double borderRadius;
  final double elevation;
  final double? width;
  final double? height;
  final void Function()? onPressed;
  final BoxDecoration? decoration;
  final Widget? icon;
  final String iconPosition; // NEW PARAMETER: 'left' or 'right'
  final Color? borderColor;
  final double? fontSize;

  const CommonElevatedButton({
    super.key,
    required this.text,
    this.buttonColor = kPrimaryColor,
    this.shadowColor,
    this.textColor = kWhiteColor,
    this.borderRadius = 10.0,
    this.elevation = 0.0,
    this.width,
    this.height,
    this.decoration,
    required this.onPressed,
    this.icon,
    this.iconPosition = 'left', // Default to left
    this.borderColor,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width:  width ?? 396.w,
        height: height ?? 48.h,
        decoration:
            decoration ??
            BoxDecoration(
              color: buttonColor,
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: borderColor ?? Colors.transparent,
                width: 1,
              ),
              boxShadow:
                  elevation > 0
                      ? [
                        BoxShadow(
                          color: shadowColor ?? Colors.black.withOpacity(0.2),
                          blurRadius: elevation,
                          offset: const Offset(0, 2),
                        ),
                      ]
                      : null,
            ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Show icon on the left if iconPosition is 'left'
            if (icon != null && iconPosition == 'left') ...[
              icon!,
              const SizedBox(width: 8.0),
            ],
            Text(
              text,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: fontSize ?? 16.0,
                fontWeight: FontWeight.w400,
                color: textColor,
              ),
            ),
            // Show icon on the right if iconPosition is 'right'
            if (icon != null && iconPosition == 'right') ...[
              const SizedBox(width: 8.0),
              icon!,
            ],
          ],
        ),
      ),
    );
  }
}
