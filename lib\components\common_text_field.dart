import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gatepass_flutter/utils/colors.dart';

class CommonTextFormField extends StatelessWidget {
  const CommonTextFormField({
    super.key,
    this.controller,
    this.onSaved,
    this.validator,
    this.hintText,
    this.maxLines = 1,
    this.suffixIcon,
    this.obscureText = false,
    this.readOnly = false,
    this.minLines,
    this.height,
    this.prefixIcon,
    this.label,
    this.keyboardType,
  });

  final TextEditingController? controller;
  final void Function(String?)? onSaved;
  final String? Function(String?)? validator;
  final String? hintText;
  final int? maxLines;
  final Widget? suffixIcon;
  final bool obscureText;
  final bool readOnly;
  final int? minLines;
  final double? height;
  final Widget? prefixIcon;
  final String? label;
  final TextInputType? keyboardType;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 5,
      children: [
        if (label != null)
          Text(
            label ?? '',
            style: TextStyle(
              color: kTextColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        TextFormField(
          obscureText: obscureText,
          controller: controller,
          readOnly: readOnly,
          onSaved: onSaved,
          validator: validator,
          cursorHeight: 18,
          keyboardType: keyboardType,
          
          decoration: InputDecoration(
            fillColor: kWhiteColor,
            contentPadding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            filled: true,
            hintText: hintText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(
                color: kBorderColor.withValues(alpha: 0.22),
              ),
            ),
            suffixIcon: suffixIcon,
            prefixIcon: prefixIcon,
          ),
          maxLines: maxLines,
          minLines: minLines,
        ),
      ],
    );
  }
}
