import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive/hive.dart';
import 'package:provider/provider.dart';
import 'package:gatepass_flutter/common/controllers/auth_provider.dart';
import 'package:gatepass_flutter/utils/app_text.dart';
import 'package:gatepass_flutter/utils/colors.dart';
import 'package:gatepass_flutter/utils/app_router.dart';

late Box box;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => AuthProvider())],
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return MaterialApp.router(
                title: AppText.appName,
                debugShowCheckedModeBanner: false,
                theme: ThemeData(
                  colorScheme: ColorScheme.fromSeed(seedColor: kPrimaryColor),
                  useMaterial3: true,
                ),
                routerConfig: AppRouter.createRouter(authProvider),
                builder: EasyLoading.init(),
              );
            },
          );
        },
      ),
    );
  }
}
